apiVersion: serving.knative.dev/v1
kind: Service
metadata:
  name: splade-gpu
  annotations:
    run.googleapis.com/ingress: all
    run.googleapis.com/ingress-status: all
spec:
  template:
    metadata:
      annotations:
        autoscaling.knative.dev/maxScale: '3'
        run.googleapis.com/cpu-throttling: 'false'
        run.googleapis.com/gpu-zonal-redundancy-disabled: 'true'
        run.googleapis.com/execution-environment: gen2
    spec:
      containers:
      - image: us-central1-docker.pkg.dev/spladecontainer/splade/splade-gpu:latest
        ports:
        - containerPort: 8080
          name: http1
        resources:
          limits:
            cpu: '4'
            memory: '16Gi'
            nvidia.com/gpu: '1'
        startupProbe:
          failureThreshold: 1800
          periodSeconds: 1
          tcpSocket:
            port: 8080
          timeoutSeconds: 1
      nodeSelector:
        run.googleapis.com/accelerator: nvidia-l4
      containerConcurrency: 1
      timeoutSeconds: 3600

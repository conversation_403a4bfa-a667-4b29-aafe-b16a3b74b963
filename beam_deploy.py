from beam import App, Runtime, Image, Output
import subprocess
import time

# Create Beam image using your existing container from Artifact Registry
image = Image(
    python_version="python3.11",  # Match the Python version in your Dockerfile
    base_image="us-central1-docker.pkg.dev/spladecontainer/splade/splade-gpu:latest",
    base_image_creds=["GCP_ACCESS_TOKEN"],
)

# Create Beam app
app = App(
    name="splade-gpu-service",
    runtime=Runtime(
        image=image,
        cpu=4,
        memory="16Gi",
        gpu="T4",
    ),
)

def start_fastapi_server():
    """Start the FastAPI server in the background"""
    import subprocess
    import time
    import requests

    print("Starting FastAPI server...")

    # Start the FastAPI server
    process = subprocess.Popen([
        "uvicorn", "app:app",
        "--host", "0.0.0.0",
        "--port", "8080"
    ])

    # Wait for server to start
    for i in range(60):  # Wait up to 60 seconds
        try:
            response = requests.get("http://localhost:8080/health", timeout=2)
            if response.status_code == 200:
                print("FastAPI server started successfully")
                return process
        except Exception as e:
            print(f"Waiting for server... attempt {i+1}/60")
            time.sleep(1)

    raise Exception("FastAPI server failed to start")

@app.run(
    cpu=4,
    memory="16Gi",
    gpu="T4",
    image=image,
    on_start=start_fastapi_server,
)
def splade_embed(text: str):
    """
    Splade v3 embedding endpoint on Beam
    """
    import requests
    import json

    print(f"Processing text: {text}")

    # Call the FastAPI service running inside the container
    response = requests.post(
        "http://localhost:8080/embed",
        json={"text": text},
        headers={"Content-Type": "application/json"},
        timeout=30
    )

    if response.status_code == 200:
        result = response.json()
        print(f"Successfully generated embedding with {len(result.get('vector', []))} dimensions")
        return result
    else:
        error_msg = f"Request failed with status {response.status_code}"
        print(error_msg)
        return {"error": error_msg}

@app.run(
    cpu=1,
    memory="4Gi",
    image=image,
    on_start=start_fastapi_server,
)
def health_check():
    """
    Health check endpoint
    """
    import requests

    try:
        response = requests.get("http://localhost:8080/health", timeout=10)
        return {"status": "healthy", "response": response.json()}
    except Exception as e:
        return {"status": "unhealthy", "error": str(e)}

if __name__ == "__main__":
    # Test the endpoint locally
    print("Testing Splade embedding...")
    result = splade_embed("hello world")
    print(f"Result: {result}")

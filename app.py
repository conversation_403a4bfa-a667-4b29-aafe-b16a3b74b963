from fastapi import FastAP<PERSON>
from pydantic import BaseModel
from transformers import AutoTokenizer, AutoModelForMaskedLM
import torch
import logging
import os

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI()

# Global variables for lazy loading
tok = None
model = None

def load_model():
    """Load model and tokenizer lazily"""
    global tok, model
    if tok is None or model is None:
        logger.info("Loading Splade model and tokenizer...")
        try:
            tok = AutoTokenizer.from_pretrained("naver/splade-v3", cache_dir="/models")
            model = AutoModelForMaskedLM.from_pretrained("naver/splade-v3", cache_dir="/models")
            model.eval()
            logger.info("Model loaded successfully!")
        except Exception as e:
            logger.error(f"Failed to load model: {e}")
            raise
    return tok, model

class Query(BaseModel):
    text: str

@torch.inference_mode()
@app.post("/embed")
def embed(q: Query):
    # Load model on first request
    tokenizer, model_instance = load_model()

    inputs = tokenizer(q.text, return_tensors="pt", truncation=True, max_length=512)
    outputs = model_instance(**inputs)
    # SPLADE uses masked language model logits as sparse vector
    sparse = torch.relu(outputs.logits[0]).sum(dim=0).tolist()
    return {"vector": sparse}

@app.get("/health")
def health():
    return {"status": "healthy"}

@app.get("/ready")
def ready():
    """Readiness check - loads model if not already loaded"""
    try:
        load_model()
        return {"status": "ready", "model": "splade-v3"}
    except Exception as e:
        logger.error(f"Model not ready: {e}")
        return {"status": "not ready", "error": str(e)}

@app.get("/")
def root():
    return {"message": "Splade v3 Embedding Service", "endpoints": ["/embed", "/health", "/ready"]}

from fastapi import FastAPI
from pydantic import BaseModel
from transformers import AutoTokenizer, AutoModelForMaskedLM
import torch

app = FastAPI()
tok = AutoTokenizer.from_pretrained("naver/splade-cocondenser-ensembledistil")
model = AutoModelForMaskedLM.from_pretrained("naver/splade-cocondenser-ensembledistil")
model.eval()

class Query(BaseModel):
    text: str

@torch.inference_mode()
@app.post("/embed")
def embed(q: Query):
    inputs = tok(q.text, return_tensors="pt", truncation=True, max_length=512)
    outputs = model(**inputs)
    # SPLADE uses masked language model logits as sparse vector
    sparse = torch.relu(outputs.logits[0]).sum(dim=0).tolist()
    return {"vector": sparse}

@app.get("/health")
def health():
    return {"status": "healthy"}

@app.get("/")
def root():
    return {"message": "Splade v3 Embedding Service", "endpoints": ["/embed", "/health"]}

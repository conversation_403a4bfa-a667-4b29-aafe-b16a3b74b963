# Complete Guide: From Google Account to GPU Cloud Run for **`naver/splade‑v3`**

This guide takes you from a brand-new Google Cloud account all the way to a deployed GPU-powered Splade service on Cloud Run with **instance‑based billing**, **1 × NVIDIA L4**, and **scale‑to‑zero**. At 50,000 queries/month, the GPU is live ~5,000s and costs ≈ $1.17 (Tier‑1 region).

---

## Part 1: Google Cloud Setup (First-Time Users)

Here's the **end-to-end checklist to go from a brand-new Google account to a project that can deploy GPU-backed Cloud Run services** and provides both an **API key** and a **service-account key** for automation.

### TL;DR Setup Steps

1. **Create a Google Cloud account and billing profile.**
2. **Spin up (or select) a project** and link it to billing.
3. **Install & initialise the gcloud CLI** locally.
4. **Enable the core APIs** (Cloud Run, Artifact Registry, Cloud Build, IAM, API Keys).
5. **Request GPU quota** in your target region.
6. **Create two credentials:** an **API key** (for per-request auth) and a **service-account JSON key** (for CI/CD or local `gcloud` automation).
7. **Lock them down** with IAM roles, referrers & key restrictions.

### 1. Set up billing (one-time)

1. Sign in to the **Manage billing accounts** page and click **Create account**.([cloud.google.com][1])
2. Pick or create a **Google Payments profile**, add a credit card, and finish the wizard. You only pay when a project is linked to this billing account.([cloud.google.com][2])

> Tip: Google still gives new customers US $300 free credit and a year of "Always Free" usage for many resources (vCPU, storage, egress).

### 2. Create / select a project & link billing

1. In the Console header, open the **project selector** and click **New project** (or pick an existing one).([cloud.google.com][3])
2. Navigate to **Billing › Account Management** and press **Link a project** → choose your project. Without this, APIs stay disabled.([cloud.google.com][4])

### 3. Install and initialise the Google Cloud CLI

```bash
# macOS / Linux (APT/Yum/Homebrew options in docs)
curl https://sdk.cloud.google.com | bash
exec -l $SHELL
gcloud init          # log in & pick the project
```

_Installer & OS specifics are in the official guide._([cloud.google.com][5])
During `gcloud init` you authenticate, set `core/project`, and choose a default region/zone.([cloud.google.com][6])

Later you can switch projects with:

```bash
gcloud config set project PROJECT_ID    # change active project
```

([cloud.google.com][7])

### 4. Enable the required APIs

```bash
gcloud services enable \
    run.googleapis.com \
    artifactregistry.googleapis.com \
    cloudbuild.googleapis.com \
    iam.googleapis.com \
    iamcredentials.googleapis.com \
    apikeys.googleapis.com
```

`gcloud services enable` is the quickest way to turn on multiple APIs at once.([stackoverflow.com][8])
If you prefer the Console, each API has an **Enable** button on its overview page.([atlas-google-cloud-workshop.com][9])

### 5. Request an NVIDIA L4 GPU quota (once per region)

1. Go to **IAM & Admin › Quotas**.
2. Filter **Metric → GPUs (all regions)**, tick **NVIDIA L4**, press **Edit quotas**, enter a small number (e.g. 3), and submit. Approvals are almost instant for most accounts.([developers.google.com][10])

### 6. Create credentials

#### 6.1 Service account + JSON key (for automation & `gcloud`)

```bash
# create service account
gcloud iam service-accounts create splade-deployer \
  --description="Deploys Cloud Run services" \
  --display-name="splade-deployer"

# grant least-privilege roles
gcloud projects add-iam-policy-binding $PROJECT_ID \
  --member="serviceAccount:splade-deployer@$PROJECT_ID.iam.gserviceaccount.com" \
  --role="roles/run.admin"
gcloud projects add-iam-policy-binding $PROJECT_ID \
  --member="serviceAccount:splade-deployer@$PROJECT_ID.iam.gserviceaccount.com" \
  --role="roles/iam.serviceAccountUser"
gcloud projects add-iam-policy-binding $PROJECT_ID \
  --member="serviceAccount:splade-deployer@$PROJECT_ID.iam.gserviceaccount.com" \
  --role="roles/artifactregistry.writer"

# generate a key file
gcloud iam service-accounts keys create key.json \
  --iam-account splade-deployer@$PROJECT_ID.iam.gserviceaccount.com
```

Key creation is also available via **IAM › Service Accounts › Keys › Add key › JSON** in Console.([cloud.google.com][11])

Store `key.json` in a secrets vault or CI secret, and locally set:

```bash
export GOOGLE_APPLICATION_CREDENTIALS=$PWD/key.json
gcloud auth activate-service-account --key-file $GOOGLE_APPLICATION_CREDENTIALS
```

([cloud.google.com][12])

#### 6.2 Standard API key (for simple REST calls)

1. Open **APIs & Services › Credentials**.
2. Click **Create credentials › API key**. A 40-character key appears.([cloud.google.com][13], [developers.google.com][14])
3. Immediately hit **Restrict key**:

   - **API restrictions** → choose only the APIs this key should reach (e.g. API Gateway, API Keys API).
   - **Application restrictions** → HTTP referrers, IP addresses or Android/iOS package names as needed.

API keys are best for public-facing services or quick tests; prefer OAuth tokens or service-account auth when possible.([cloud.google.com][15])

### 7. Create an Artifact Registry Docker repo

```bash
gcloud artifacts repositories create splade \
  --repository-format=docker \
  --location=us-central1 \
  --description="Docker repo for Splade images"
```

Once created, push with `docker push us-central1-docker.pkg.dev/$PROJECT_ID/splade/IMAGE:tag`.([cloud.google.com][16])

### 8. Verify IAM & Cloud Run access

- In **Cloud Run › Settings › Permissions**, ensure the runtime service account (default or custom) has **Cloud Run Invoker** if you plan to call the service with an API key.
- Test a dry-run with:

```bash
gcloud run services list          # should work with your service account
```

### 9. Setup Complete!

Following the steps above sets up a secure, billed, quota-ready Google Cloud environment with both API and service-account credentials—everything you need to automate builds and call your future Cloud Run endpoints.

---

## Part 2: Deploy Splade Service

Now that your Google Cloud environment is ready, let's deploy the Splade service.

### Prerequisites

If you followed **Part 1** above, you should have:

- **Google Cloud account** with billing enabled and project configured
- **gcloud CLI** installed and authenticated
- **At least 3 × NVIDIA L4 GPU quota** approved in your chosen region
- **Required APIs enabled** (Cloud Run, Artifact Registry, etc.)
- **Service account credentials** created
- **Artifact Registry repository** created
- Python 3.10+, Git, Docker/BuildKit, and jq locally

If you're starting with an existing setup, ensure you have:

- Google Cloud project with billing enabled in _us‑central1_ (or other Tier‑1 region)
- gcloud >= 472, docker/BuildKit, and jq locally
- At least **3 × NVIDIA L4 GPU quota** in the chosen region

---

## 1. Bootstrap a working folder

```bash
git clone https://huggingface.co/naver/splade-v3
cd splade-v3
python -m venv .venv && source .venv/bin/activate
pip install 'torch>=2.2' transformers fastapi uvicorn[standard]
```

`transformers` will fetch weights (~268 MB) at first run and cache them under `/root/.cache/huggingface`. citeturn0search9

---

## 2. Minimal FastAPI wrapper (`app.py`)

```python
from fastapi import FastAPI
from pydantic import BaseModel
from transformers import AutoTokenizer, AutoModelForMaskedLM
import torch

app = FastAPI()
tok = AutoTokenizer.from_pretrained("naver/splade-v3")
model = AutoModelForMaskedLM.from_pretrained("naver/splade-v3")
model.eval()

class Query(BaseModel):
    text: str

@torch.inference_mode()
@app.post("/embed")
def embed(q: Query):
    inputs = tok(q.text, return_tensors="pt", truncation=True, max_length=512)
    outputs = model(**inputs)
    # SPLADE uses masked language model logits as sparse vector
    sparse = torch.relu(outputs.logits[0]).sum(dim=0).tolist()
    return {"vector": sparse}
```

Concurrency is purposely **1** so Cloud Run does not hold the GPU longer than necessary. citeturn0search5

---

## 3. Dockerfile

```dockerfile
# syntax=docker/dockerfile:1.4
FROM python:3.11-slim as base

ENV DEBIAN_FRONTEND=noninteractive     PYTHONUNBUFFERED=1     TRANSFORMERS_CACHE=/models     HF_HUB_DISABLE_SYMLINKS_WARNING=1

RUN apt-get update && apt-get install -y --no-install-recommends git && rm -rf /var/lib/apt/lists/*

WORKDIR /app
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

COPY app.py .

# download weights at build time to avoid cold start hit
RUN python - <<'PY'
from transformers import AutoModelForMaskedLM, AutoTokenizer
AutoTokenizer.from_pretrained("naver/splade-v3", cache_dir="/models")
AutoModelForMaskedLM.from_pretrained("naver/splade-v3", cache_dir="/models")
PY

EXPOSE 8080
ENTRYPOINT ["uvicorn", "app:app", "--host=0.0.0.0", "--port=8080", "--workers=1"]
```

`requirements.txt`

```
torch>=2.2
transformers>=4.41
fastapi
uvicorn[standard]
```

The image is ≈ 1 GB after squashing layers. citeturn0search4

---

## 4. Build & test locally

```bash
export PROJECT_ID=$(gcloud config get-value project)
docker build -t us-central1-docker.pkg.dev/$PROJECT_ID/splade/splade-gpu:latest .
docker run -p 8080:8080 us-central1-docker.pkg.dev/$PROJECT_ID/splade/splade-gpu
curl -X POST localhost:8080/embed -d '{"text":"hello world"}' -H "Content-Type: application/json"
```

---

## 5. Push to Artifact Registry

```bash
gcloud artifacts repositories create splade --repository-format=docker --location=us-central1 || true
docker push us-central1-docker.pkg.dev/$PROJECT_ID/splade/splade-gpu:latest
```

Artifact Registry stores the image privately in region. citeturn0search7

---

## 6. Deploy to Cloud Run (GPU, cost-optimized for light usage)

### Option A: Request-Based Billing (Recommended for Light Usage)

```bash
gcloud run deploy splade-gpu \
  --region=us-central1 \
  --image=us-central1-docker.pkg.dev/$PROJECT_ID/splade/splade-gpu:latest \
  --platform=managed \
  --cpu=4 \
  --memory=16Gi \
  --gpu-count=1 \
  --gpu-type=nvidia-l4 \
  --concurrency=1 \
  --timeout=60 \
  --min-instances=0 \
  --max-instances=1 \
  --ingress=internal-and-cloud-load-balancing \
  --allow-unauthenticated
```

- **Request-based billing** (default): Pay per request + actual compute time only
- **`--min-instances=0`** ensures true scale-to-zero with no idle costs
- **`--max-instances=1`** prevents cost spikes for light usage
- **Perfect for:** <100 requests/month (~$0.10-0.50 total cost)

### Option B: Instance-Based Billing (Better for Heavy Usage)

```bash
gcloud run deploy splade-gpu \
  --region=us-central1 \
  --image=us-central1-docker.pkg.dev/$PROJECT_ID/splade/splade-gpu:latest \
  --platform=managed \
  --billing-mode=instance \
  --cpu=4 \
  --memory=16Gi \
  --gpu-count=1 \
  --gpu-type=nvidia-l4 \
  --concurrency=1 \
  --timeout=60 \
  --max-instances=3 \
  --ingress=internal-and-cloud-load-balancing \
  --allow-unauthenticated
```

- **`--billing-mode=instance`** eliminates per‑request fees; only vCPU/RAM/GPU seconds charged
- **Better for:** >10,000 requests/month (~$1.17 total cost)
- With GPUs the service still **scales to 0** after idle minutes, restarting on next request

---

## 7. Verify & benchmark

```bash
ENDPOINT=$(gcloud run services describe splade-gpu --format="value(status.url)")
hey -n 50000 -c 10 -m POST -H "Content-Type: application/json" -d '{"text":"foo bar"}' $ENDPOINT/embed
```

Monitor runtime seconds and cost in **Cloud Cost Management → Cloud Run → Splade‑gpu**.

**Cost examples:**

- **Request-based:** Light usage (100 requests/month) = ~$0.10-0.50 total
- **Instance-based:** Heavy usage (50,000 requests/month) = GPU at 5,000s × $0.000233 = ~$1.17 per month

---

## 8. Tweaks

| Goal                          | Knob                                            | Notes                                                  |
| ----------------------------- | ----------------------------------------------- | ------------------------------------------------------ |
| **Lowest cost (light usage)** | Use **Option A** with `--min-instances=0`       | Request-based billing, perfect for <100 requests/month |
| **Lowest cost (heavy usage)** | Use **Option B** with `--billing-mode=instance` | Instance-based billing, better for >10K requests/month |
| Higher throughput             | `--concurrency=8` and batch in app              | Cloud Run concurrency limit 1000                       |
| Faster cold‑start             | `--min-instances=1` (adds ~$600/month)          | Keeps 1 instance always warm                           |
| Cheaper after steady‑state    | buy **FlexCUD** 3‑year L4 commitment            | GPU pricing discounts                                  |

---

**Success!** Your Splade endpoint is now deployed with cost-optimized billing:

- **Light usage:** Request-based billing (~$0.10-0.50/month for 100 requests)
- **Heavy usage:** Instance-based billing (~$1.17/month for 50K requests)
- **GPU time:** Only ~0.10s of actual GPU time per call

---

## 9. Next Steps & Best Practices

- **Deploy your container** following the steps above and monitor logs under **Cloud Run › Logs Explorer**.
- **Rotate credentials periodically:** Delete old API keys and service-account keys in **IAM › Service Accounts › Keys**.([cloud.google.com][11])
- **Tighten security:** Add VPC egress controls and NAT for production workloads.([googlecloudcommunity.com][17])
- **Monitor costs:** Watch GPU usage patterns in **Cloud Cost Management** to optimize your `--max-instances` and `--concurrency` settings.

---

## References

[1]: https://cloud.google.com/billing/docs/how-to/create-billing-account?utm_source=chatgpt.com "Create a new self-serve Cloud Billing account - Google Cloud"
[2]: https://cloud.google.com/billing/docs/how-to/modify-project?utm_source=chatgpt.com "Enable, disable, or change billing for a project - Google Cloud"
[3]: https://cloud.google.com/api-gateway/docs/get-started-cloud-run?utm_source=chatgpt.com "Getting started with API Gateway and Cloud Run"
[4]: https://cloud.google.com/run/docs/tutorials/gcloud?utm_source=chatgpt.com "gcloud command line inside a Cloud Run service tutorial"
[5]: https://cloud.google.com/sdk/docs/install?utm_source=chatgpt.com "Install the gcloud CLI | Google Cloud SDK Documentation"
[6]: https://cloud.google.com/sdk/docs/initializing?utm_source=chatgpt.com "Initializing the gcloud CLI | Google Cloud SDK Documentation"
[7]: https://cloud.google.com/sdk/gcloud/reference/config/set?utm_source=chatgpt.com "gcloud config set | Google Cloud SDK Documentation"
[8]: https://stackoverflow.com/questions/********/how-can-one-use-gcloud-to-enable-apis?utm_source=chatgpt.com "How Can One Use gcloud To Enable APIs - Stack Overflow"
[9]: https://www.atlas-google-cloud-workshop.com/docs/cloud-run/enable-apis?utm_source=chatgpt.com "Enable Cloud APIs with Cloud Shell"
[10]: https://developers.google.com/workspace/guides/enable-apis?utm_source=chatgpt.com "Enable Google Workspace APIs - Google for Developers"
[11]: https://cloud.google.com/iam/docs/keys-create-delete?utm_source=chatgpt.com "Create and delete service account keys - IAM - Google Cloud"
[12]: https://cloud.google.com/sdk/gcloud/reference/auth/activate-service-account?utm_source=chatgpt.com "gcloud auth activate-service-account"
[13]: https://cloud.google.com/docs/authentication/api-keys?utm_source=chatgpt.com "Manage API keys | Authentication - Google Cloud"
[14]: https://developers.google.com/maps/documentation/javascript/get-api-key?utm_source=chatgpt.com "Use API Keys | Maps JavaScript API - Google for Developers"
[15]: https://cloud.google.com/api-keys/docs/get-started-api-keys?utm_source=chatgpt.com "Getting started with API Keys API - Google Cloud"
[16]: https://cloud.google.com/artifact-registry/docs/docker/pushing-and-pulling?utm_source=chatgpt.com "Push and pull images | Artifact Registry documentation - Google Cloud"
[17]: https://www.googlecloudcommunity.com/gc/Serverless/Configure-an-API-Key-for-use-in-Cloud-Run/m-p/908502?utm_source=chatgpt.com "Solved: Re: Configure an API Key for use in Cloud Run"

///
/// Generated 28 Jun 2025

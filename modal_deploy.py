import modal
import os

# Create Modal app
app = modal.App("splade-gpu-service")

# Convert Dockerfile to Modal Image
# Based on your Dockerfile, we need:
# 1. Python 3.11 base (python:3.11-slim)
# 2. Git installation
# 3. Python requirements
# 4. App files
# 5. Model download with HF token
# 6. Environment variables

def download_splade_model():
    """Download Splade model during image build"""
    import os
    from huggingface_hub import login
    from transformers import AutoModelForMaskedLM, AutoTokenizer
    
    # Login with HuggingFace token
    hf_token = os.environ.get("HF_TOKEN")
    if hf_token:
        login(hf_token)
    
    print("Downloading Splade v3 model and tokenizer...")
    
    # Download model and tokenizer to /models directory
    tokenizer = AutoTokenizer.from_pretrained('naver/splade-v3', cache_dir='/models')
    model = AutoModelForMaskedLM.from_pretrained('naver/splade-v3', cache_dir='/models')
    
    print("Model download completed!")

# Create Modal image equivalent to your Dockerfile
image = (
    modal.Image.debian_slim(python_version="3.11")  # FROM python:3.11-slim
    .apt_install("git")  # RUN apt-get install git
    .env({
        "DEBIAN_FRONTEND": "noninteractive",
        "PYTHONUNBUFFERED": "1",
        "TRANSFORMERS_CACHE": "/models",
        "HF_HUB_DISABLE_SYMLINKS_WARNING": "1"
    })
    .pip_install_from_requirements("requirements.txt")  # Install from requirements.txt
    .workdir("/app")  # Set working directory
    .run_function(
        download_splade_model,
        secrets=[modal.Secret.from_dict({"HF_TOKEN": "*************************************"})]
    )
    .add_local_file("app.py", "/app/app.py")  # Copy app.py (must be last)
)

# Define the main endpoint function
@app.function(
    image=image,
    gpu="T4",  # Add GPU support
    memory=16384,  # 16GB memory
    cpu=4,
    timeout=3600,
    scaledown_window=300,
)
@modal.concurrent(max_inputs=1)
@modal.fastapi_endpoint(method="POST", label="splade-embed")
def embed_endpoint(text: str):
    """
    Splade v3 embedding endpoint
    """
    import subprocess
    import time
    import requests
    import json
    
    # Start FastAPI server in background
    print("Starting FastAPI server...")
    process = subprocess.Popen([
        "uvicorn", "app:app", 
        "--host", "0.0.0.0", 
        "--port", "8080",
        "--workers", "1"
    ])
    
    # Wait for server to start
    for i in range(30):
        try:
            response = requests.get("http://localhost:8080/health", timeout=2)
            if response.status_code == 200:
                print("FastAPI server started successfully")
                break
        except:
            time.sleep(1)
    else:
        raise Exception("FastAPI server failed to start")
    
    try:
        # Call the embedding endpoint
        response = requests.post(
            "http://localhost:8080/embed",
            json={"text": text},
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        
        if response.status_code == 200:
            return response.json()
        else:
            return {"error": f"Request failed with status {response.status_code}"}
    
    finally:
        # Clean up
        process.terminate()

# Alternative: Direct function without FastAPI wrapper
@app.function(
    image=image,
    gpu="T4",
    memory=16384,
    cpu=4,
    timeout=3600,
)
def splade_embed_direct(text: str):
    """
    Direct Splade embedding without FastAPI wrapper
    """
    import torch
    from transformers import AutoTokenizer, AutoModelForMaskedLM
    
    # Load model and tokenizer (they should be cached from build)
    tokenizer = AutoTokenizer.from_pretrained("naver/splade-v3", cache_dir="/models")
    model = AutoModelForMaskedLM.from_pretrained("naver/splade-v3", cache_dir="/models")
    model.eval()
    
    # Generate embedding
    with torch.inference_mode():
        inputs = tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
        outputs = model(**inputs)
        # SPLADE uses masked language model logits as sparse vector
        sparse = torch.relu(outputs.logits[0]).sum(dim=0).tolist()
    
    return {"vector": sparse, "text": text}

# Health check endpoint
@app.function(image=image)
@modal.fastapi_endpoint(method="GET", label="health")
def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "service": "splade-v3"}

if __name__ == "__main__":
    # Test the direct function locally
    with app.run():
        result = splade_embed_direct.remote("hello world")
        print(f"Test result: {result}")

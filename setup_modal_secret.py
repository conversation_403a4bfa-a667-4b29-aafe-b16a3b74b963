import modal

# Create HuggingFace secret for Modal
# You need to run this once to set up the secret

def create_hf_secret():
    """Create HuggingFace secret in Modal"""

    # Your HuggingFace token
    hf_token = "*************************************"

    # Create the secret
    secret = modal.Secret.from_dict({
        "HF_TOKEN": hf_token
    })

    # Deploy the secret with a name
    secret.deploy("huggingface-secret")

    print("✅ HuggingFace secret created successfully!")
    print("Secret name: 'huggingface-secret'")

if __name__ == "__main__":
    create_hf_secret()

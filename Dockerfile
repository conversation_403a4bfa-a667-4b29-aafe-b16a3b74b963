# syntax=docker/dockerfile:1.4
FROM python:3.11-slim as base

ENV DEBIAN_FRONTEND=noninteractive \
    PYTHONUNBUFFERED=1 \
    TRANSFORMERS_CACHE=/models \
    HF_HUB_DISABLE_SYMLINKS_WARNING=1

RUN apt-get update && apt-get install -y --no-install-recommends git && rm -rf /var/lib/apt/lists/*

WORKDIR /app
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

COPY app.py .

# download weights at build time to avoid cold start hit
ARG HF_TOKEN
RUN python -c "from huggingface_hub import login; from transformers import AutoModelForMaskedLM, AutoTokenizer; login('$HF_TOKEN'); AutoTokenizer.from_pretrained('naver/splade-v3', cache_dir='/models'); AutoModelForMaskedLM.from_pretrained('naver/splade-v3', cache_dir='/models')"

EXPOSE 8080
ENTRYPOINT ["uvicorn", "app:app", "--host=0.0.0.0", "--port=8080", "--workers=1"]

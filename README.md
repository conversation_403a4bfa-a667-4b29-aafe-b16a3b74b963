# Splade v3 GPU Cloud Run Deployment

This repository contains everything needed to deploy the naver/splade-v3 model as a GPU-powered Google Cloud Run service with cost-optimized billing and scale-to-zero capabilities.

## 🚀 Quick Start

### Prerequisites

- Google Cloud CLI installed and configured
- Docker installed
- Google Cloud project with billing enabled
- NVIDIA L4 GPU quota in us-central1 region

### 1. Build the Docker Image

```bash
export PROJECT_ID=spladecontainer
docker build --build-arg HF_TOKEN=your_huggingface_token \
  -t us-central1-docker.pkg.dev/$PROJECT_ID/splade/splade-gpu:latest .
```

### 2. Deploy with Request-Based Billing (Recommended)

For light to moderate usage with automatic scale-to-zero:

```bash
./deploy.sh
```

### 3. Deploy with Instance-Based Billing (Heavy Usage)

For consistent heavy traffic where you want guaranteed availability:

```bash
./deploy-instance-billing.sh
```

## 📊 Cost Optimization

### Request-Based Billing (Default)

- **Best for**: Light to moderate usage, sporadic traffic
- **Scaling**: 0-10 instances, scales to zero when idle
- **Cost**: Pay only for actual requests and compute time
- **Cold starts**: ~30-60 seconds for first request after idle period

### Instance-Based Billing

- **Best for**: Heavy consistent usage, latency-sensitive applications
- **Scaling**: 1-10 instances, always at least 1 running
- **Cost**: Continuous billing for minimum instance + additional requests
- **Cold starts**: Minimal, as at least one instance is always warm

### GPU Costs (NVIDIA L4 in us-central1)

- **Request-based**: ~$0.35/hour when active
- **Instance-based**: ~$0.35/hour continuous for minimum instance
- **Additional instances**: Scale based on traffic

## 🔧 API Usage

### Health Check

```bash
curl https://your-service-url/health
```

### Generate Embeddings

```bash
curl -X POST https://your-service-url/embed \
  -H "Content-Type: application/json" \
  -d '{"text":"your text here"}'
```

### Response Format

```json
{
  "vector": [0.0, 0.0, 0.370087593793869, ...]
}
```

## 📈 Monitoring and Optimization

### View Logs

```bash
gcloud run services logs read splade-gpu --region=us-central1
```

### Monitor Costs

- Visit [Google Cloud Billing](https://console.cloud.google.com/billing)
- Set up billing alerts for cost control

### Performance Tuning

- Adjust `--concurrency` for parallel request handling
- Modify `--memory` and `--cpu` based on your workload
- Use `--min-instances` > 0 to reduce cold starts

## 🛠️ Configuration Options

### Environment Variables

- `TRANSFORMERS_CACHE`: Model cache directory (default: `/models`)
- `HF_HUB_DISABLE_SYMLINKS_WARNING`: Disable symlink warnings

### Resource Limits

- **Memory**: 16Gi (minimum required for GPU, adjustable based on model requirements)
- **CPU**: 4 vCPUs (minimum required for GPU, can be increased for higher throughput)
- **GPU**: 1x NVIDIA L4 (required for optimal performance)
- **Timeout**: 3600 seconds (1 hour max request time)
- **Zonal Redundancy**: Disabled by default to reduce costs and quota requirements

## 🔒 Security

### Authentication

The service is deployed with `--allow-unauthenticated` for easy testing. For production:

1. Remove `--allow-unauthenticated` flag
2. Use IAM for access control:

```bash
gcloud run services add-iam-policy-binding splade-gpu \
  --member="user:<EMAIL>" \
  --role="roles/run.invoker" \
  --region=us-central1
```

### API Keys

For programmatic access, create and use API keys:

```bash
gcloud alpha services api-keys create --display-name="Splade API Key"
```

## 🐛 Troubleshooting

### Common Issues

1. **Authentication errors**: Run `gcloud auth login`
2. **GPU quota**: Request NVIDIA L4 quota in us-central1
3. **Build failures**: Ensure HuggingFace token is valid
4. **Cold starts**: Consider instance-based billing for latency-sensitive apps

### Debug Commands

```bash
# Check service status
gcloud run services describe splade-gpu --region=us-central1

# View recent logs
gcloud run services logs tail splade-gpu --region=us-central1

# Test locally
docker run -p 8080:8080 us-central1-docker.pkg.dev/spladecontainer/splade/splade-gpu:latest
```

## 📚 Additional Resources

- [SPLADE Paper](https://arxiv.org/abs/2109.10086)
- [Google Cloud Run GPU Documentation](https://cloud.google.com/run/docs/configuring/services/gpu)
- [Cloud Run Pricing](https://cloud.google.com/run/pricing)
- [HuggingFace Transformers](https://huggingface.co/docs/transformers)

## 🤝 Contributing

Feel free to submit issues and enhancement requests!

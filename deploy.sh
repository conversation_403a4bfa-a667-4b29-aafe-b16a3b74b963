#!/bin/bash

# Splade Cloud Run Deployment Script
# This script deploys the Splade v3 model to Google Cloud Run with GPU support

set -e

PROJECT_ID="spladecontainer"
REGION="us-central1"
SERVICE_NAME="splade-gpu"
REPOSITORY_NAME="splade"
IMAGE_NAME="splade-gpu"
IMAGE_TAG="latest"

echo "🚀 Starting Splade deployment to Cloud Run..."

# Step 1: Authenticate with Google Cloud
echo "📝 Step 1: Authenticating with Google Cloud..."
gcloud auth login
gcloud config set project $PROJECT_ID

# Step 2: Enable required APIs
echo "🔧 Step 2: Enabling required APIs..."
gcloud services enable run.googleapis.com
gcloud services enable artifactregistry.googleapis.com
gcloud services enable cloudbuild.googleapis.com

# Step 3: Create Artifact Registry repository
echo "📦 Step 3: Creating Artifact Registry repository..."
gcloud artifacts repositories create $REPOSITORY_NAME \
    --repository-format=docker \
    --location=$REGION \
    --description="Docker repository for Splade models" || echo "Repository may already exist"

# Step 4: Configure Docker authentication
echo "🔐 Step 4: Configuring Docker authentication..."
gcloud auth configure-docker $REGION-docker.pkg.dev

# Step 5: Push Docker image
echo "📤 Step 5: Pushing Docker image to Artifact Registry..."
docker push $REGION-docker.pkg.dev/$PROJECT_ID/$REPOSITORY_NAME/$IMAGE_NAME:$IMAGE_TAG

# Step 6: Deploy to Cloud Run with request-based billing (recommended for light usage)
echo "🚀 Step 6: Deploying to Cloud Run with request-based billing..."
echo "Note: Using YAML deployment to properly configure GPU without zonal redundancy"

# Create temporary service YAML
cat > temp-service.yaml << EOF
apiVersion: serving.knative.dev/v1
kind: Service
metadata:
  name: $SERVICE_NAME
  annotations:
    run.googleapis.com/ingress: all
    run.googleapis.com/ingress-status: all
spec:
  template:
    metadata:
      annotations:
        autoscaling.knative.dev/maxScale: '10'
        run.googleapis.com/cpu-throttling: 'false'
        run.googleapis.com/gpu-zonal-redundancy-disabled: 'true'
        run.googleapis.com/execution-environment: gen2
    spec:
      containers:
      - image: $REGION-docker.pkg.dev/$PROJECT_ID/$REPOSITORY_NAME/$IMAGE_NAME:$IMAGE_TAG
        ports:
        - containerPort: 8080
          name: http1
        resources:
          limits:
            cpu: '4'
            memory: '16Gi'
            nvidia.com/gpu: '1'
        startupProbe:
          failureThreshold: 1800
          periodSeconds: 1
          tcpSocket:
            port: 8080
          timeoutSeconds: 1
      nodeSelector:
        run.googleapis.com/accelerator: nvidia-l4
      containerConcurrency: 1
      timeoutSeconds: 3600
EOF

gcloud run services replace temp-service.yaml --region=$REGION
rm temp-service.yaml

echo "✅ Deployment complete!"

# Get the service URL
SERVICE_URL=$(gcloud run services describe $SERVICE_NAME --region=$REGION --format="value(status.url)")
echo "🌐 Service URL: $SERVICE_URL"

# Test the deployment
echo "🧪 Testing the deployment..."
echo "Health check:"
curl -s "$SERVICE_URL/health" | jq .

echo ""
echo "Embedding test:"
curl -s -X POST "$SERVICE_URL/embed" \
    -H "Content-Type: application/json" \
    -d '{"text":"hello world"}' | jq '.vector | length'

echo ""
echo "🎉 Splade service is now deployed and ready to use!"
echo "📊 Monitor costs at: https://console.cloud.google.com/billing"
echo "📈 View logs at: https://console.cloud.google.com/run/detail/$REGION/$SERVICE_NAME/logs"

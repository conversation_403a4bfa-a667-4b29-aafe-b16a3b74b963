#!/bin/bash

# Splade Cloud Run Deployment Script
# This script deploys the Splade v3 model to Google Cloud Run with GPU support

set -e

PROJECT_ID="spladecontainer"
REGION="us-central1"
SERVICE_NAME="splade-gpu"
REPOSITORY_NAME="splade"
IMAGE_NAME="splade-gpu"
IMAGE_TAG="latest"

echo "🚀 Starting Splade deployment to Cloud Run..."

# Step 1: Authenticate with Google Cloud
echo "📝 Step 1: Authenticating with Google Cloud..."
gcloud auth login
gcloud config set project $PROJECT_ID

# Step 2: Enable required APIs
echo "🔧 Step 2: Enabling required APIs..."
gcloud services enable run.googleapis.com
gcloud services enable artifactregistry.googleapis.com
gcloud services enable cloudbuild.googleapis.com

# Step 3: Create Artifact Registry repository
echo "📦 Step 3: Creating Artifact Registry repository..."
gcloud artifacts repositories create $REPOSITORY_NAME \
    --repository-format=docker \
    --location=$REGION \
    --description="Docker repository for Splade models" || echo "Repository may already exist"

# Step 4: Configure Docker authentication
echo "🔐 Step 4: Configuring Docker authentication..."
gcloud auth configure-docker $REGION-docker.pkg.dev

# Step 5: Push Docker image
echo "📤 Step 5: Pushing Docker image to Artifact Registry..."
docker push $REGION-docker.pkg.dev/$PROJECT_ID/$REPOSITORY_NAME/$IMAGE_NAME:$IMAGE_TAG

# Step 6: Deploy to Cloud Run with request-based billing (recommended for light usage)
echo "🚀 Step 6: Deploying to Cloud Run with request-based billing..."
gcloud run deploy $SERVICE_NAME \
    --image=$REGION-docker.pkg.dev/$PROJECT_ID/$REPOSITORY_NAME/$IMAGE_NAME:$IMAGE_TAG \
    --platform=managed \
    --region=$REGION \
    --allow-unauthenticated \
    --memory=8Gi \
    --cpu=4 \
    --gpu=1 \
    --gpu-type=nvidia-l4 \
    --max-instances=10 \
    --min-instances=0 \
    --concurrency=1 \
    --timeout=3600 \
    --cpu-boost \
    --execution-environment=gen2

echo "✅ Deployment complete!"

# Get the service URL
SERVICE_URL=$(gcloud run services describe $SERVICE_NAME --region=$REGION --format="value(status.url)")
echo "🌐 Service URL: $SERVICE_URL"

# Test the deployment
echo "🧪 Testing the deployment..."
echo "Health check:"
curl -s "$SERVICE_URL/health" | jq .

echo ""
echo "Embedding test:"
curl -s -X POST "$SERVICE_URL/embed" \
    -H "Content-Type: application/json" \
    -d '{"text":"hello world"}' | jq '.vector | length'

echo ""
echo "🎉 Splade service is now deployed and ready to use!"
echo "📊 Monitor costs at: https://console.cloud.google.com/billing"
echo "📈 View logs at: https://console.cloud.google.com/run/detail/$REGION/$SERVICE_NAME/logs"

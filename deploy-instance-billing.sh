#!/bin/bash

# Splade Cloud Run Deployment Script - Instance-Based Billing
# This script deploys the Splade v3 model to Cloud Run with instance-based billing
# Use this for heavy usage scenarios where you expect consistent traffic

set -e

PROJECT_ID="spladecontainer"
REGION="us-central1"
SERVICE_NAME="splade-gpu-instance"
REPOSITORY_NAME="splade"
IMAGE_NAME="splade-gpu"
IMAGE_TAG="latest"

echo "🚀 Starting Splade deployment to Cloud Run with instance-based billing..."

# Step 1: Authenticate with Google Cloud (if not already done)
echo "📝 Step 1: Checking authentication..."
gcloud config set project $PROJECT_ID

# Step 2: Deploy to Cloud Run with instance-based billing
echo "🚀 Step 2: Deploying to Cloud Run with instance-based billing..."
echo "Note: Using YAML deployment to properly configure GPU without zonal redundancy"

# Create temporary service YAML
cat > temp-service-instance.yaml << EOF
apiVersion: serving.knative.dev/v1
kind: Service
metadata:
  name: $SERVICE_NAME
  annotations:
    run.googleapis.com/ingress: all
    run.googleapis.com/ingress-status: all
spec:
  template:
    metadata:
      annotations:
        autoscaling.knative.dev/maxScale: '10'
        autoscaling.knative.dev/minScale: '1'
        run.googleapis.com/cpu-throttling: 'false'
        run.googleapis.com/gpu-zonal-redundancy-disabled: 'true'
        run.googleapis.com/execution-environment: gen2
    spec:
      containers:
      - image: $REGION-docker.pkg.dev/$PROJECT_ID/$REPOSITORY_NAME/$IMAGE_NAME:$IMAGE_TAG
        ports:
        - containerPort: 8080
          name: http1
        resources:
          limits:
            cpu: '4'
            memory: '16Gi'
            nvidia.com/gpu: '1'
        startupProbe:
          failureThreshold: 1800
          periodSeconds: 1
          tcpSocket:
            port: 8080
          timeoutSeconds: 1
      nodeSelector:
        run.googleapis.com/accelerator: nvidia-l4
      containerConcurrency: 1
      timeoutSeconds: 3600
EOF

gcloud run services replace temp-service-instance.yaml --region=$REGION
rm temp-service-instance.yaml

echo "✅ Instance-based deployment complete!"

# Get the service URL
SERVICE_URL=$(gcloud run services describe $SERVICE_NAME --region=$REGION --format="value(status.url)")
echo "🌐 Service URL: $SERVICE_URL"

# Test the deployment
echo "🧪 Testing the deployment..."
echo "Health check:"
curl -s "$SERVICE_URL/health" | jq .

echo ""
echo "Embedding test:"
curl -s -X POST "$SERVICE_URL/embed" \
    -H "Content-Type: application/json" \
    -d '{"text":"hello world"}' | jq '.vector | length'

echo ""
echo "🎉 Splade service with instance-based billing is now deployed!"
echo "💰 Note: This configuration will incur costs even when idle due to min-instances=1"
echo "📊 Monitor costs at: https://console.cloud.google.com/billing"
echo "📈 View logs at: https://console.cloud.google.com/run/detail/$REGION/$SERVICE_NAME/logs"

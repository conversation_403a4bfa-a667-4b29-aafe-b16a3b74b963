#!/bin/bash

# Splade Cloud Run Deployment Script - Instance-Based Billing
# This script deploys the Splade v3 model to Cloud Run with instance-based billing
# Use this for heavy usage scenarios where you expect consistent traffic

set -e

PROJECT_ID="spladecontainer"
REGION="us-central1"
SERVICE_NAME="splade-gpu-instance"
REPOSITORY_NAME="splade"
IMAGE_NAME="splade-gpu"
IMAGE_TAG="latest"

echo "🚀 Starting Splade deployment to Cloud Run with instance-based billing..."

# Step 1: Authenticate with Google Cloud (if not already done)
echo "📝 Step 1: Checking authentication..."
gcloud config set project $PROJECT_ID

# Step 2: Deploy to Cloud Run with instance-based billing
echo "🚀 Step 2: Deploying to Cloud Run with instance-based billing..."
gcloud run deploy $SERVICE_NAME \
    --image=$REGION-docker.pkg.dev/$PROJECT_ID/$REPOSITORY_NAME/$IMAGE_NAME:$IMAGE_TAG \
    --platform=managed \
    --region=$REGION \
    --allow-unauthenticated \
    --memory=8Gi \
    --cpu=4 \
    --gpu=1 \
    --gpu-type=nvidia-l4 \
    --max-instances=10 \
    --min-instances=1 \
    --concurrency=1 \
    --timeout=3600 \
    --cpu-boost \
    --execution-environment=gen2 \
    --cpu-throttling=false

echo "✅ Instance-based deployment complete!"

# Get the service URL
SERVICE_URL=$(gcloud run services describe $SERVICE_NAME --region=$REGION --format="value(status.url)")
echo "🌐 Service URL: $SERVICE_URL"

# Test the deployment
echo "🧪 Testing the deployment..."
echo "Health check:"
curl -s "$SERVICE_URL/health" | jq .

echo ""
echo "Embedding test:"
curl -s -X POST "$SERVICE_URL/embed" \
    -H "Content-Type: application/json" \
    -d '{"text":"hello world"}' | jq '.vector | length'

echo ""
echo "🎉 Splade service with instance-based billing is now deployed!"
echo "💰 Note: This configuration will incur costs even when idle due to min-instances=1"
echo "📊 Monitor costs at: https://console.cloud.google.com/billing"
echo "📈 View logs at: https://console.cloud.google.com/run/detail/$REGION/$SERVICE_NAME/logs"
